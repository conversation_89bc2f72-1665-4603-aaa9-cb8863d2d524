@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.625rem;
}

.dark {
  --background: 264.695 42% 12.9%;
  --foreground: 247.858 3% 98.4%;
  --card: 259.21 40% 14%;
  --card-foreground: 247.858 3% 98.4%;
  --popover: 265.755 42% 20.8%;
  --popover-foreground: 247.858 3% 98.4%;
  --primary: 220 150% 70%; /* Brighter blue for better chart visibility */
  --primary-foreground: 264.695 42% 12.9%;
  --secondary: 259.21 40% 14%;
  --secondary-foreground: 247.858 3% 98.4%;
  --muted: 259.21 40% 14%;
  --muted-foreground: 257.417 46% 55.4%;
  --accent: 259.21 40% 14%;
  --accent-foreground: 247.858 3% 98.4%;
  --destructive: 27.325 245% 57.7%;
  --border: 259.21 40% 14%;
  --input: 259.21 40% 14%;
  --ring: 256.788 40% 70.4%;
  --chart-1: 220 150% 70%;
  --chart-2: 184.704 118% 60%;
  --chart-3: 227.392 70% 39.8%;
  --chart-4: 84.429 189% 82.8%;
  --chart-5: 70.08 188% 76.9%;
}

.transflow-light {
  /* Clean transflow-light theme with reliable hex colors */
  --background: #f8f9fb;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  --primary: #1e40af; /* main transflow blue */
  --primary-foreground: #f8f9fb;
  --secondary: #e2e8f0;
  --secondary-foreground: #1e40af;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #1e40af; /* Blue for sidebar */
  --accent-foreground: #f8f9fb;
  --destructive: #dc2626;
  --border: #d1d5db;
  --input: #f1f5f9;
  --ring: #1e40af;
  --chart-1: #1e40af; /* Primary blue */
  --chart-2: #0891b2; /* Complementary teal */
  --chart-3: #7c3aed; /* Purple accent */
  --chart-4: #ea580c; /* Orange accent */
  --chart-5: #16a34a; /* Green accent */

  /* Sidebar specific colors */
  --sidebar: var(--accent); /* Blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on blue */
  --sidebar-primary: #1e3a8a; /* Darker blue for primary elements */
  --sidebar-primary-foreground: #f8f9fb;
  --sidebar-accent: #3b82f6; /* Slightly lighter blue for hover states */
  --sidebar-accent-foreground: #f8f9fb;
  --sidebar-border: #1e40af; /* Darker blue borders in sidebar */
  --sidebar-ring: #1e3a8a;
}

.transflow-dark {
  --background: 240 100% 8%; /* Very dark blue background */
  --foreground: 240 50% 92%; /* Light blue-tinted text */
  --card: 240 50% 12%; /* Dark blue cards */
  --card-foreground: 240 50% 92%;
  --popover: 240 50% 15%; /* Slightly lighter dark blue for popovers */
  --popover-foreground: 240 50% 92%;
  --primary: 240 70% 65%; /* Bright blue for visibility in dark mode */
  --primary-foreground: 240 100% 8%; /* Dark text on bright blue */
  --secondary: 240 50% 20%; /* Dark blue secondary */
  --secondary-foreground: 240 50% 85%;
  --muted: 240 50% 18%; /* Dark blue muted */
  --muted-foreground: 240 50% 60%; /* Medium blue for muted text */
  --accent: 240 50% 25%; /* Dark blue for sidebar */
  --accent-foreground: 240 50% 85%; /* Light text on dark blue sidebar */
  --destructive: 15 70% 65%; /* Bright red for visibility */
  --border: 240 50% 25%; /* Dark blue borders */
  --input: 240 50% 15%; /* Dark blue inputs */
  --ring: 240 70% 65%;
  --chart-1: 240 70% 65%; /* Bright blue for main chart - good contrast */
  --chart-2: 180 60% 70%; /* Bright teal */
  --chart-3: 300 70% 75%; /* Bright purple */
  --chart-4: 60 80% 70%; /* Bright orange */
  --chart-5: 120 60% 70%; /* Bright green */

  /* Sidebar specific colors */
  --sidebar: var(--accent); /* Dark blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on dark blue */
  --sidebar-primary: 240 70% 65%; /* Bright blue for primary elements */
  --sidebar-primary-foreground: 240 100% 8%;
  --sidebar-accent: 240 60% 35%; /* Medium blue for hover states */
  --sidebar-accent-foreground: 240 50% 92%;
  --sidebar-border: 240 50% 20%; /* Subtle borders in sidebar */
  --sidebar-ring: 240 70% 65%;
}

@theme inline {
  --font-inter: 'Inter', 'sans-serif';
  --font-manrope: 'Manrope', 'sans-serif';
}

@layer base {
  * {
    border-color: var(--border);
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
  html {
    overflow-x: hidden;
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    min-height: 100vh;
    width: 100%;
    font-family: var(--font-inter);
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

/* Custom utilities */
.container {
  margin-inline: auto;
  padding-inline: 2rem;
}

/* Responsive container with proper padding */
.responsive-container {
  margin-inline: auto;
  padding-inline: 1rem;
  max-width: 100%;
}

@media (min-width: 640px) {
  .responsive-container {
    padding-inline: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    padding-inline: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    padding-inline: 2.5rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    padding-inline: 3rem;
  }
}

/* Responsive grid utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 1.75rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Dashboard specific responsive grid */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .dashboard-grid {
    gap: 1.25rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    gap: 1.75rem;
    grid-template-columns: repeat(8, 1fr);
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

@media (max-width: 767px) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

/* Responsive text sizing */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1.125rem;
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.375rem;
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.625rem;
}

.responsive-text-lg {
  font-size: 1.125rem;
  line-height: 1.875rem;
}

.responsive-text-xl {
  font-size: 1.25rem;
  line-height: 1.875rem;
}

.responsive-text-2xl {
  font-size: 1.375rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  .responsive-text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .responsive-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .responsive-text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .responsive-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .responsive-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .responsive-text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

/* Responsive spacing utilities */
.responsive-p-sm {
  padding: 0.75rem;
}

.responsive-p-md {
  padding: 1rem;
}

.responsive-p-lg {
  padding: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-p-sm {
    padding: 1rem;
  }

  .responsive-p-md {
    padding: 1.5rem;
  }

  .responsive-p-lg {
    padding: 2rem;
  }
}

/* Responsive margin utilities */
.responsive-m-sm {
  margin: 0.75rem;
}

.responsive-m-md {
  margin: 1rem;
}

.responsive-m-lg {
  margin: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-m-sm {
    margin: 1rem;
  }

  .responsive-m-md {
    margin: 1.5rem;
  }

  .responsive-m-lg {
    margin: 2rem;
  }
}

/* Responsive gap utilities */
.responsive-gap-sm {
  gap: 0.75rem;
}

.responsive-gap-md {
  gap: 1rem;
}

.responsive-gap-lg {
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-gap-sm {
    gap: 1rem;
  }

  .responsive-gap-md {
    gap: 1.5rem;
  }

  .responsive-gap-lg {
    gap: 2rem;
  }
}

/* Hide/show utilities for responsive design */
.mobile-only {
  display: block;
}

.tablet-up {
  display: none;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .tablet-up {
    display: block;
  }
}

@media (min-width: 1024px) {
  .desktop-only {
    display: block;
  }
}

/* Responsive flex utilities */
.responsive-flex-col {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .responsive-flex-col {
    flex-direction: row;
  }
}

.responsive-flex-row {
  display: flex;
  flex-direction: row;
}

@media (max-width: 767px) {
  .responsive-flex-row {
    flex-direction: column;
  }
}

.no-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-scrollbar: none;
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* styles.css */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}